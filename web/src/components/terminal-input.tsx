"use client";

import type React from "react";
import { v4 as uuidv4 } from "uuid";
import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { ArrowUp, Loader2 } from "lucide-react";
import { Button } from "./ui/button";
import { useStream } from "@langchain/langgraph-sdk/react";
import { useRouter } from "next/navigation";
import { HumanMessage } from "@langchain/core/messages";
import { toast } from "sonner";
import { DEFAULT_AGENT } from "@/common/constants";

interface TerminalInputProps {
  placeholder?: string;
  disabled?: boolean;
  apiUrl: string;
  assistantId: string;
  selectedAgent?: string;
}

export function TerminalInput({
  placeholder = "描述您的编程任务或提出问题...",
  disabled = false,
  apiUrl,
  assistantId,
  selectedAgent = DEFAULT_AGENT,
}: TerminalInputProps) {
  const { push } = useRouter();
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const stream = useStream({
    apiUrl,
    assistantId,
    reconnectOnMount: true,
    fetchStateHistory: false,
  });

  const handleSend = async () => {
    setLoading(true);

    const trimmedMessage = message.trim();

    if (trimmedMessage.length > 0) {
      const newHumanMessage = new HumanMessage({
        id: uuidv4(),
        content: trimmedMessage,
      });

      try {
        const newThreadId = uuidv4();
        
        const run = await stream.client.runs.create(
          newThreadId,
          assistantId,
          {
            input: {
              input: trimmedMessage,
              messages: [newHumanMessage],
            },
            config: {
              recursion_limit: 400,
            },
            ifNotExists: "create",
            streamResumable: true,
            streamMode: ["values", "messages-tuple", "custom"],
          },
        );

        // set session storage so the stream can be resumed after redirect.
        sessionStorage.setItem(`lg:stream:${newThreadId}`, run.run_id);

        // Store the initial message for optimistic rendering
        try {
          const initialMessageData = {
            message: newHumanMessage,
            timestamp: new Date().toISOString(),
          };
          sessionStorage.setItem(
            `lg:initial-message:${newThreadId}`,
            JSON.stringify(initialMessageData),
          );
        } catch (error) {
          // If sessionStorage fails, continue without optimistic rendering
          console.error(
            "Failed to store initial message in sessionStorage:",
            error,
          );
        }

        push(`/chat/${newThreadId}?agent=${selectedAgent}`);
        setMessage("");
      } catch (e) {
        console.error("Failed to create thread:", e);
        toast.error("发送消息失败", {
          description: "请检查网络连接和服务状态",
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="border-border bg-muted hover:border-muted-foreground/50 hover:bg-muted/80 focus-within:border-muted-foreground/70 focus-within:bg-muted/80 focus-within:shadow-muted-foreground/20 rounded-md border p-2 font-mono text-xs transition-all duration-200 focus-within:shadow-md">
      <div className="text-foreground flex items-center gap-1">
        <div className="border-border bg-background/50 flex items-center gap-1 rounded-md border p-1 transition-colors duration-200">
          <span className="text-muted-foreground">langgraph-mvp</span>
          <span className="text-muted-foreground/70">@</span>
          <span className="text-muted-foreground">local</span>
        </div>

        {/* Prompt */}
        <span className="text-muted-foreground">$</span>

        <Button
          onClick={handleSend}
          disabled={disabled || !message.trim() || loading}
          size="icon"
          variant="default"
          className="ml-auto size-8 rounded-full border border-white/20 transition-all duration-200 hover:border-white/30 disabled:border-transparent"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <ArrowUp className="size-4" />
          )}
        </Button>
      </div>

      {/* Multiline Input */}
      <div className="my-2 flex gap-2">
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder={placeholder}
          disabled={disabled || loading}
          className="text-foreground placeholder:text-muted-foreground focus:placeholder:text-muted-foreground/60 max-h-[50vh] min-h-[80px] flex-1 resize-none border-none bg-transparent p-0 font-mono text-xs shadow-none transition-all duration-200 focus-visible:ring-0 focus-visible:ring-offset-0"
          rows={6}
        />
      </div>

      {/* Help text */}
      <div className="text-muted-foreground mt-1 text-xs">
        Press Cmd+Enter to send
      </div>
    </div>
  );
}
