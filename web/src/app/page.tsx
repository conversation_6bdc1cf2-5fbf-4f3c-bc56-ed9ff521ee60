"use client";

import { useRouter } from "next/navigation";
import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ThemeToggle } from "@/components/theme-toggle";
import { Plus, Settings, Archive } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import { AVAILABLE_AGENTS, DEFAULT_AGENT } from "@/common/constants";
import { DEFAULT_API_URL } from "@/providers/client";

import { useThreadsSWR } from "@/hooks/useThreadsSWR";
import { threadsToMetadata } from "@/lib/thread-utils";
import { toast } from "sonner";
import { TerminalInput } from "@/components/terminal-input";

// 线程卡片组件 - 参考open-swe的ThreadCard
function ThreadCard({ thread, onClick }: { thread: any; onClick: () => void }) {
  return (
    <Card
      className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-l-primary"
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-sm truncate">
              {thread.title || `线程 ${thread.id.slice(0, 8)}`}
            </h3>
            <p className="text-xs text-muted-foreground mt-1 font-mono">
              ID: {thread.id}
            </p>
            <div className="mt-2 space-y-1">
              <p className="text-xs text-muted-foreground">
                更新时间: {new Date(thread.updatedAt).toLocaleString("zh-CN")}
              </p>
              {thread.status && (
                <div className="flex items-center gap-1">
                  <div className={`h-2 w-2 rounded-full ${
                    thread.status === 'running' ? 'bg-yellow-500' :
                    thread.status === 'completed' ? 'bg-green-500' : 'bg-gray-500'
                  }`} />
                  <span className="text-xs text-muted-foreground capitalize">
                    {thread.status}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// 线程卡片加载状态
function ThreadCardLoading() {
  return (
    <Card className="animate-pulse">
      <CardContent className="p-4">
        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-muted rounded w-1/2 mb-2"></div>
        <div className="h-3 bg-muted rounded w-2/3"></div>
      </CardContent>
    </Card>
  );
}

export default function HomePage() {
  const router = useRouter();
  const [selectedAgent, setSelectedAgent] = useState<string>(DEFAULT_AGENT);

  // 获取线程列表 - 参考open-swe的useThreadsSWR
  const { threads, isLoading: threadsLoading } = useThreadsSWR({
    assistantId: selectedAgent,
    disableUserFiltering: true,
  });

  // 转换线程数据为显示格式
  const threadsMetadata = useMemo(() => threadsToMetadata(threads), [threads]);
  const displayThreads = threadsMetadata.slice(0, 4);

  const handleThreadClick = (threadId: string) => {
    router.push(`/chat/${threadId}?agent=${selectedAgent}`);
  };

  const handleThreads = () => {
    router.push(`/threads`);
  };

  return (
    <div className="flex flex-1 flex-col min-h-screen">
      {/* Header */}
      <div className="border-border bg-card border-b px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h1 className="text-xl font-bold text-foreground">
              LangGraph MVP Demo
            </h1>
            <p className="text-muted-foreground text-sm hidden md:block">
              基于LangGraph的智能Agent服务平台
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-xs">ready</span>
              <div className="h-1 w-1 rounded-full bg-green-500 dark:bg-green-600"></div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="mx-auto max-w-4xl space-y-6 p-4">

          {/* Agent选择区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center flex items-center justify-center gap-2">
                <Settings className="h-5 w-5" />
                选择智能助手
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">助手类型</label>
                <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择一个助手" />
                  </SelectTrigger>
                  <SelectContent>
                    {AVAILABLE_AGENTS.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        <div className="flex items-center gap-2">
                          <agent.icon className="h-4 w-4" />
                          {agent.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 显示选中agent的描述 */}
              {selectedAgent && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm text-muted-foreground">
                    {AVAILABLE_AGENTS.find(agent => agent.id === selectedAgent)?.description}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 聊天输入区域 - 参考open-swe的TerminalInput */}
          <Card className="border-border bg-card">
            <CardContent className="p-4">
              <div className="space-y-3">
                <TerminalInput
                  placeholder="描述您的编程任务或提出问题..."
                  apiUrl={DEFAULT_API_URL}
                  assistantId={selectedAgent}
                  selectedAgent={selectedAgent}
                />
              </div>
            </CardContent>
          </Card>

          {/* Recent & Running Threads */}
          <div>
            <div className="mb-3 flex items-center justify-between">
              <h2 className="text-foreground text-base font-semibold">
                最近的对话
              </h2>
              <Button
                variant="outline"
                size="sm"
                className="border-border text-muted-foreground hover:text-foreground h-7 text-xs"
                onClick={handleThreads}
              >
                <Plus className="h-3 w-3 mr-1" />
                所有线程
              </Button>
            </div>

            {threadsLoading || threads.length ? (
              <div className="grid gap-3 md:grid-cols-2">
                {threadsLoading && threads.length === 0 && (
                  <>
                    <Card className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                      </CardContent>
                    </Card>
                    <Card className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                      </CardContent>
                    </Card>
                  </>
                )}
                {displayThreads.map((thread) => (
                  <ThreadCard
                    key={thread.id}
                    thread={thread}
                    onClick={() => handleThreadClick(thread.id)}
                  />
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center py-8">
                <span className="text-muted-foreground flex items-center gap-2">
                  <Archive className="size-4" />
                  <span className="text-sm">暂无对话记录</span>
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
